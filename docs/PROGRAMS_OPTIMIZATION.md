# Optimisation des requêtes Programs avec conditions sur les Podcasts

## Problème initial

La méthode `setCurrentPrograms()` devait récupérer les programmes ayant au moins un podcast associé avec les conditions suivantes :
- Le podcast doit avoir un fichier audio (via `hasAudio()`)
- Le podcast doit être actif (`active = true`)
- Le podcast doit être publié (`published_at <= now()`)
- Condition sur la station de radio basée sur les podcasts

## Solutions implémentées

### 1. Version Eloquent optimisée (Recommandée)

```php
private function getCurrentProgramsWithEloquent(): Collection
{
    $podcastConditions = function (Builder $query) {
        $query->hasAudio()
            ->active()
            ->where('published_at', '<=', Date::now())
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereHas('radioStations', function (Builder $radioQuery) {
                    $radioQuery->where('radio_stations.id', $this->selectedRadioStationId);
                })->orWhereDoesntHave('radioStations');
            });
    };

    return Program::with(['media'])
        ->withCount(['podcasts' => $podcastConditions])
        ->withMax(['podcasts' => $podcastConditions], 'published_at')
        ->whereHas('podcasts', $podcastConditions)
        ->orderByRaw('podcasts_max_published_at DESC NULLS LAST')
        ->orderBy('title')
        ->limit(20)
        ->get();
}
```

**Avantages :**
- Code lisible et maintenable
- Utilise les relations Eloquent
- Bénéficie des optimisations Laravel
- Facile à déboguer

### 2. Version SQL brute (Performance maximale)

```php
private function getCurrentProgramsOptimized(): Collection
{
    // Requête SQL optimisée avec toutes les conditions combinées
    // Voir le code complet dans le fichier
}
```

**Avantages :**
- Performance maximale
- Contrôle total sur la requête SQL
- Une seule requête pour tout récupérer
- Optimisé pour les gros volumes de données

## Index de performance ajoutés

```sql
-- Index composite pour les conditions principales des podcasts
CREATE INDEX idx_podcasts_active_published ON podcasts (active, published_at);
CREATE INDEX idx_podcasts_published_active ON podcasts (published_at, active);
CREATE INDEX idx_podcasts_type_active ON podcasts (type, active);

-- Index pour la recherche des médias audio
CREATE INDEX idx_media_model_collection ON media (model_type, model_id, collection_name);

-- Index pour les jointures avec les stations de radio
CREATE INDEX idx_podcasts_radio_stations_radio_id ON podcasts_radio_stations (radio_station_id);
```

## Méthode hasAudio() optimisée

La méthode `hasAudio()` du modèle Podcast utilise déjà une approche optimisée :

```php
public function scopeHasAudio(Builder $podcast): void
{
    $podcast->where(function (Builder $query) {
        $query->whereIn('id', function (\Illuminate\Database\Query\Builder $query) {
            $query->select('media.model_id')
                ->from('media')
                ->join('podcasts', 'podcasts.id', '=', 'media.model_id')
                ->where('media.model_type', self::class)
                ->where('media.collection_name', 'audio')
                ->groupBy('media.model_id');
        });
        $query->orWhere('winmedia_audio_source_uploaded', true);
    });
}
```

## Recommandations

1. **Utiliser la version Eloquent** pour la plupart des cas d'usage
2. **Passer à la version SQL brute** si les performances deviennent critiques
3. **Appliquer la migration des index** pour améliorer les performances
4. **Monitorer les performances** avec les outils Laravel (Telescope, Debugbar)

## Tests de performance

Pour tester les performances, vous pouvez utiliser :

```php
// Dans un test ou une commande Artisan
$start = microtime(true);
$programs = $this->getCurrentProgramsWithEloquent();
$eloquentTime = microtime(true) - $start;

$start = microtime(true);
$programs = $this->getCurrentProgramsOptimized();
$sqlTime = microtime(true) - $start;

echo "Eloquent: {$eloquentTime}s, SQL: {$sqlTime}s";
```
