# Optimisation des requêtes Programs avec logique UNION

## Requête SQL cible

```sql
SELECT programs.id, programs.title
FROM programs
INNER JOIN program_recurrences 
    ON program_recurrences.program_id = programs.id

UNION

SELECT programs.id, programs.title
FROM programs
INNER JOIN podcasts 
    ON podcasts.program_id = programs.id
WHERE podcasts.type = 'original'
  AND podcasts.published_at > now() - interval '6 months';
```

## Solutions implémentées

### 1. Version Eloquent avec deux requêtes séparées

```php
private function getCurrentProgramsWithEloquent(): Collection
{
    // Première partie : programmes avec récurrences
    $programsWithRecurrences = Program::select('id', 'title')
        ->whereHas('recurrences')
        ->get()
        ->keyBy('id');

    // Deuxième partie : programmes avec podcasts originaux récents
    $programsWithOriginalPodcasts = Program::select('id', 'title')
        ->whereHas('podcasts', function (Builder $query) {
            $query->where('type', 'original')
                ->where('published_at', '>', Date::now()->subMonths(6));
        })
        ->get()
        ->keyBy('id');

    // Union des deux collections
    $allProgramIds = $programsWithRecurrences->keys()
        ->merge($programsWithOriginalPodcasts->keys())
        ->unique();

    // Récupération finale avec toutes les relations
    return Program::with(['media', 'podcasts', 'recurrences'])
        ->whereIn('id', $allProgramIds)
        ->orderBy('title')
        ->get();
}
```

**Avantages :**
- Code très lisible et compréhensible
- Logique UNION explicite
- Facile à déboguer chaque partie séparément

**Inconvénients :**
- 3 requêtes au total (2 + 1 finale)

### 2. Version Eloquent optimisée avec une seule requête

```php
private function getCurrentProgramsOptimizedSingleQuery(): Collection
{
    return Program::with(['media', 'podcasts', 'recurrences'])
        ->where(function (Builder $query) {
            $query->whereHas('recurrences')
                ->orWhereHas('podcasts', function (Builder $podcastQuery) {
                    $podcastQuery->where('type', 'original')
                        ->where('published_at', '>', Date::now()->subMonths(6));
                });
        })
        ->orderBy('title')
        ->get();
}
```

**Avantages :**
- Une seule requête Eloquent
- Performance optimale avec Eloquent
- Code concis

**Inconvénients :**
- Logique UNION moins explicite

### 3. Version SQL brute (Performance maximale)

```php
private function getCurrentProgramsWithRawSQL(): Collection
{
    $sql = "
        SELECT programs.id, programs.title
        FROM programs
        INNER JOIN program_recurrences 
            ON program_recurrences.program_id = programs.id

        UNION

        SELECT programs.id, programs.title
        FROM programs
        INNER JOIN podcasts 
            ON podcasts.program_id = programs.id
        WHERE podcasts.type = 'original'
          AND podcasts.published_at > now() - interval '6 months'
        
        ORDER BY title
    ";

    $results = DB::select($sql);
    $programIds = collect($results)->pluck('id');

    return Program::with(['media', 'podcasts', 'recurrences'])
        ->whereIn('id', $programIds)
        ->orderBy('title')
        ->get();
}
```

**Avantages :**
- Performance maximale
- Contrôle total sur la requête
- Implémentation exacte de votre SQL

**Inconvénients :**
- Moins maintenable
- Dépendant de la base de données

## Recommandations

1. **Version 2 (Single Query)** : Recommandée pour la plupart des cas
   - Bon équilibre performance/lisibilité
   - Une seule requête Eloquent
   - Facile à maintenir

2. **Version 1 (Deux requêtes)** : Pour la lisibilité maximale
   - Quand la logique métier doit être très explicite
   - Pour le débogage et les tests

3. **Version 3 (SQL brute)** : Pour les performances critiques
   - Gros volumes de données
   - Optimisations spécifiques à la base de données

## Index recommandés

```sql
-- Pour les récurrences
CREATE INDEX idx_program_recurrences_program_id ON program_recurrences (program_id);

-- Pour les podcasts originaux
CREATE INDEX idx_podcasts_type_published ON podcasts (type, published_at);
CREATE INDEX idx_podcasts_program_type_published ON podcasts (program_id, type, published_at);
```

## Tests de performance

Vous pouvez tester les performances avec :

```php
$start = microtime(true);
$result1 = $this->getCurrentProgramsWithEloquent();
$time1 = microtime(true) - $start;

$start = microtime(true);
$result2 = $this->getCurrentProgramsOptimizedSingleQuery();
$time2 = microtime(true) - $start;

$start = microtime(true);
$result3 = $this->getCurrentProgramsWithRawSQL();
$time3 = microtime(true) - $start;

echo "Version 1: {$time1}s, Version 2: {$time2}s, Version 3: {$time3}s";
```
