<?php

namespace Tests\Feature\Programs;

use App\Http\Livewire\Programs\Index;
use App\Models\Audio\Podcast;
use App\Models\Radio\Program;
use App\Models\Radio\RadioStation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Date;
use Livewire\Livewire;
use Tests\TestCase;

class ProgramsOptimizationTest extends TestCase
{
    use RefreshDatabase;

    private RadioStation $radioStation;
    private Program $programWithValidPodcast;
    private Program $programWithoutValidPodcast;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer une station de radio
        $this->radioStation = RadioStation::factory()->create();

        // Créer un programme avec un podcast valide
        $this->programWithValidPodcast = Program::factory()->create();
        $validPodcast = Podcast::factory()
            ->for($this->programWithValidPodcast)
            ->create([
                'active' => true,
                'published_at' => Date::now()->subHour(),
                'winmedia_audio_source_uploaded' => true, // Simule hasAudio()
            ]);
        $validPodcast->radioStations()->attach($this->radioStation);

        // Créer un programme avec un podcast invalide (inactif)
        $this->programWithoutValidPodcast = Program::factory()->create();
        Podcast::factory()
            ->for($this->programWithoutValidPodcast)
            ->create([
                'active' => false, // Inactif
                'published_at' => Date::now()->subHour(),
                'winmedia_audio_source_uploaded' => true,
            ]);
    }

    /** @test */
    public function it_returns_only_programs_with_valid_podcasts()
    {
        $component = Livewire::test(Index::class);
        $component->call('init');
        $component->set('selectedRadioStationId', $this->radioStation->id);
        $component->call('setCurrentPrograms');

        $currentPrograms = $component->get('currentPrograms');

        // Vérifier que seul le programme avec podcast valide est retourné
        $this->assertCount(1, $currentPrograms);
        $this->assertEquals($this->programWithValidPodcast->id, $currentPrograms->first()->id);
    }

    /** @test */
    public function it_excludes_programs_with_inactive_podcasts()
    {
        $component = Livewire::test(Index::class);
        $component->call('init');
        $component->set('selectedRadioStationId', $this->radioStation->id);
        $component->call('setCurrentPrograms');

        $currentPrograms = $component->get('currentPrograms');
        $programIds = $currentPrograms->pluck('id');

        // Vérifier que le programme avec podcast inactif n'est pas inclus
        $this->assertNotContains($this->programWithoutValidPodcast->id, $programIds);
    }

    /** @test */
    public function it_includes_programs_with_podcasts_without_radio_station()
    {
        // Créer un programme avec un podcast sans station de radio associée
        $programWithoutStation = Program::factory()->create();
        Podcast::factory()
            ->for($programWithoutStation)
            ->create([
                'active' => true,
                'published_at' => Date::now()->subHour(),
                'winmedia_audio_source_uploaded' => true,
            ]);
        // Ne pas associer de station de radio

        $component = Livewire::test(Index::class);
        $component->call('init');
        $component->set('selectedRadioStationId', $this->radioStation->id);
        $component->call('setCurrentPrograms');

        $currentPrograms = $component->get('currentPrograms');
        $programIds = $currentPrograms->pluck('id');

        // Vérifier que le programme sans station est inclus
        $this->assertContains($programWithoutStation->id, $programIds);
    }

    /** @test */
    public function it_excludes_future_podcasts()
    {
        // Créer un programme avec un podcast futur
        $programWithFuturePodcast = Program::factory()->create();
        Podcast::factory()
            ->for($programWithFuturePodcast)
            ->create([
                'active' => true,
                'published_at' => Date::now()->addHour(), // Futur
                'winmedia_audio_source_uploaded' => true,
            ]);

        $component = Livewire::test(Index::class);
        $component->call('init');
        $component->set('selectedRadioStationId', $this->radioStation->id);
        $component->call('setCurrentPrograms');

        $currentPrograms = $component->get('currentPrograms');
        $programIds = $currentPrograms->pluck('id');

        // Vérifier que le programme avec podcast futur n'est pas inclus
        $this->assertNotContains($programWithFuturePodcast->id, $programIds);
    }

    /** @test */
    public function it_orders_programs_by_latest_podcast_date()
    {
        // Créer un deuxième programme avec un podcast plus récent
        $newerProgram = Program::factory()->create(['title' => 'A_Newer_Program']);
        $newerPodcast = Podcast::factory()
            ->for($newerProgram)
            ->create([
                'active' => true,
                'published_at' => Date::now()->subMinutes(30), // Plus récent
                'winmedia_audio_source_uploaded' => true,
            ]);
        $newerPodcast->radioStations()->attach($this->radioStation);

        $component = Livewire::test(Index::class);
        $component->call('init');
        $component->set('selectedRadioStationId', $this->radioStation->id);
        $component->call('setCurrentPrograms');

        $currentPrograms = $component->get('currentPrograms');

        // Vérifier que le programme avec le podcast le plus récent est en premier
        $this->assertEquals($newerProgram->id, $currentPrograms->first()->id);
    }
}
