<?php

namespace App\Http\Requests\Programs;

use App\Http\Requests\Traits\HasLocationInfo;
use App\Models\Audio\Thematic;
use App\Models\Radio\Program;
use App\Models\Users\User;
use App\Rules\TagsLength;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ProgramUpdateRequest extends FormRequest
{
    use HasLocationInfo;

    public function rules(): array
    {
        return array_merge(
            [
                'cover' => app(Program::class)->getMediaValidationRules('cover'),
                'title' => ['required', 'string', 'max:255'],
                'tags' => ['required', 'array', 'min:1', new TagsLength(65535)],
                'description' => ['required', 'string', 'max:65535'],
                'duration_hours' => ['required', 'int', 'min:0'],
                'duration_seconds' => ['required', 'int', 'min:0'],
                'duration_minutes' => ['required', 'int', 'min:0'],
                'duration' => ['required', 'int', 'min:1'],
                'thematic_id' => ['required', 'integer', Rule::exists(Thematic::class, 'id')],
                'start_podcast_title' => ['nullable', 'string', 'max:255'],
                'start_podcast_artist' => ['nullable', 'string', 'max:255'],
                'start_podcast_version' => ['nullable', 'string', 'max:255'],
                'end_podcast_title' => ['nullable', 'string', 'max:255'],
                'end_podcast_artist' => ['nullable', 'string', 'max:255'],
                'end_podcast_version' => ['nullable', 'string', 'max:255'],
                'authors_ids' => ['present', 'array'],
                'authors_ids.*' => ['required', Rule::exists(User::class, 'id')],
                'theorical_timing_live_display' => ['required', 'boolean'],
            ],
            $this->locationRules(),
        );
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'tags' => collect(explode(',', $this->tags))->filter()->map(function (string $tag) {
                return trim(Str::replace('"', '', $tag));
            })->toArray(),
            // Conversion of hours/minutes/seconds in seconds
            'duration_hours' => $this->duration_hours ?? 0,
            'duration_minutes' => $this->duration_minutes ?? 0,
            'duration_seconds' => $this->duration_seconds ?? 0,
            'duration' => Date::now()
                ->startOfDay()
                ->hour($this->duration_hours ?? 0)
                ->minute($this->duration_minutes ?? 0)
                ->second($this->duration_seconds ?? 0)
                ->secondsSinceMidnight(),
            'authors_ids' => json_decode($this->authors_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'theorical_timing_live_display' => (bool) $this->theorical_timing_live_display,
        ]);
        $this->prepareLocationValidation();
    }

    public function attributes(): array
    {
        return $this->locationAttributes();
    }
}
