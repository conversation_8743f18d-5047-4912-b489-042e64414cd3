<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use App\Models\Radio\Program;

class DashboardController extends Controller
{
    public function index(): View
    {
        SEOTools::setTitle('Tableau de bord');

        dd($this->getCurrentProgramsWithEloquent()->pluck('title'));

        return view('templates.admin.dashboard.index');
    }

    private function getCurrentProgramsWithEloquent(): Collection
    {
        // Conditions communes pour les podcasts avec toutes les optimisations
        $podcastConditions = function (Builder $query) {
            /** @phpstan-ignore-next-line */
            $query->hasAudio()
                ->active()
                ->where('published_at', '<=', Date::now())
                ->where('published_at', '>', Date::now()->subMonths(6))
                ->where(function (Builder $subWhereQuery) {
                    // Condition optimisée sur la station de radio
                    $subWhereQuery->whereHas('radioStations', function (Builder $radioQuery) {
                        $radioQuery->where('radio_stations.id', 1);
                    })->orWhereDoesntHave('radioStations');
                });
        };

        return Program::with(['media', 'podcasts'])
            //->withCount(['podcasts' => $podcastConditions])
            ->withMax(['podcasts' => $podcastConditions], 'published_at')
            ->whereHas('podcasts', $podcastConditions)
            ->orderByRaw('podcasts_max_published_at DESC NULLS LAST')
            ->orderBy('title')
            //->limit(50)
            ->get();
    }
}
