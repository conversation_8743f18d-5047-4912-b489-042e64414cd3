<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use App\Models\Radio\Program;

class DashboardController extends Controller
{
    public function index(): View
    {
        SEOTools::setTitle('Tableau de bord');

        dd($this->getCurrentProgramsWithEloquent());

        return view('templates.admin.dashboard.index');
    }

    private function getCurrentProgramsWithEloquent(): Collection
    {
        // Conditions communes pour tous les podcasts
        $basePodcastConditions = function (Builder $query) {
            /** @phpstan-ignore-next-line */
            $query->hasAudio()
                ->active()
                ->where('published_at', '<=', Date::now())
                ->where(function (Builder $subWhereQuery) {
                    // Condition optimisée sur la station de radio
                    $subWhereQuery->whereHas('radioStations', function (Builder $radioQuery) {
                        $radioQuery->where('radio_stations.id', 1);
                    })->orWhereDoesntHave('radioStations');
                });
        };

        return Program::with(['media', 'podcasts'])
            ->withMax(['podcasts' => $basePodcastConditions], 'published_at')
            ->where(function (Builder $query) use ($basePodcastConditions) {
                // Condition 1 : programmes avec récurrences ET podcasts respectant les conditions de base
                $query->where(function (Builder $subQuery) use ($basePodcastConditions) {
                    $subQuery->whereHas('recurrences')
                        ->whereHas('podcasts', $basePodcastConditions);
                })
                // OU Condition 2 : programmes avec podcasts originaux récents
                ->orWhereHas('podcasts', function (Builder $podcastQuery) use ($basePodcastConditions) {
                    $basePodcastConditions($podcastQuery);
                    $podcastQuery->where('type', 'original')
                        ->where('published_at', '>', Date::now()->subMonths(6));
                });
            })
            ->orderByRaw('podcasts_max_published_at DESC NULLS LAST')
            ->orderBy('title')
            ->get();
    }
}
