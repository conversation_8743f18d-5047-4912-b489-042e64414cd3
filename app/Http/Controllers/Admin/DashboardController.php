<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use App\Models\Radio\Program;

class DashboardController extends Controller
{
    public function index(): View
    {
        SEOTools::setTitle('Tableau de bord');

        // Test des trois versions
        $version1 = $this->getCurrentProgramsWithEloquent();
        $version2 = $this->getCurrentProgramsOptimizedSingleQuery();
        $version3 = $this->getCurrentProgramsWithRawSQL();

        dd([
            'version1_count' => $version1->count(),
            'version2_count' => $version2->count(),
            'version3_count' => $version3->count(),
            'version1_titles' => $version1->pluck('title')->sort()->values(),
            'version2_titles' => $version2->pluck('title')->sort()->values(),
            'version3_titles' => $version3->pluck('title')->sort()->values(),
        ]);

        return view('templates.admin.dashboard.index');
    }

    private function getCurrentProgramsWithEloquent(): Collection
    {
        // Version optimisée qui implémente la logique UNION :
        // 1. Programmes avec récurrences OU
        // 2. Programmes avec podcasts originaux récents (6 derniers mois)

        // Première partie : programmes avec récurrences
        $programsWithRecurrences = Program::select('id', 'title')
            ->whereHas('recurrences')
            ->get()
            ->keyBy('id');

        // Deuxième partie : programmes avec podcasts originaux récents
        $programsWithOriginalPodcasts = Program::select('id', 'title')
            ->whereHas('podcasts', function (Builder $query) {
                $query->where('type', 'original')
                    ->where('published_at', '>', Date::now()->subMonths(6));
            })
            ->get()
            ->keyBy('id');

        // Union des deux collections (évite les doublons grâce à keyBy)
        $allProgramIds = $programsWithRecurrences->keys()
            ->merge($programsWithOriginalPodcasts->keys())
            ->unique();

        // Récupération finale avec toutes les relations nécessaires
        return Program::with(['media', 'podcasts', 'recurrences'])
            ->whereIn('id', $allProgramIds)
            ->orderBy('title')
            ->get();
    }

    /**
     * Version optimisée avec une seule requête Eloquent
     * Équivalent à la requête SQL UNION mais en une seule requête
     */
    private function getCurrentProgramsOptimizedSingleQuery(): Collection
    {
        return Program::with(['media', 'podcasts', 'recurrences'])
            ->where(function (Builder $query) {
                // Condition 1 : programmes avec récurrences
                $query->whereHas('recurrences')
                    // OU Condition 2 : programmes avec podcasts originaux récents
                    ->orWhereHas('podcasts', function (Builder $podcastQuery) {
                        $podcastQuery->where('type', 'original')
                            ->where('published_at', '>', Date::now()->subMonths(6));
                    });
            })
            ->orderBy('title')
            ->get();
    }

    /**
     * Version avec requête SQL brute - Performance maximale
     * Implémente exactement votre requête UNION
     */
    private function getCurrentProgramsWithRawSQL(): Collection
    {
        $sql = "
            SELECT programs.id, programs.title
            FROM programs
            INNER JOIN program_recurrences
                ON program_recurrences.program_id = programs.id

            UNION

            SELECT programs.id, programs.title
            FROM programs
            INNER JOIN podcasts
                ON podcasts.program_id = programs.id
            WHERE podcasts.type = 'original'
              AND podcasts.published_at > now() - interval '6 months'

            ORDER BY title
        ";

        $results = DB::select($sql);
        $programIds = collect($results)->pluck('id');

        // Récupérer les modèles complets avec leurs relations
        return Program::with(['media', 'podcasts', 'recurrences'])
            ->whereIn('id', $programIds)
            ->orderBy('title')
            ->get();
    }
}
