<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\ContentLocationTrait;
use App\Http\Requests\Podcasts\PodcastStoreRequest;
use App\Http\Requests\Podcasts\PodcastUpdateRequest;
use App\Models\Audio\Podcast;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\PodcastIndexService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Date;
use Owenoj\LaravelGetId3\GetId3;

class PodcastsController extends Controller
{
    use ContentLocationTrait;

    /** @throws \ErrorException */
    public function index(Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Podcasts']));

        return view('templates.admin.podcasts.index');
    }

    /** @throws \Exception */
    public function create(): View
    {
        $podcast = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Podcast']));
        share(['api' => ['song_url' => route('api.songs.search')]]);
        $js = mix('js/templates/admin/podcasts/edit.js');

        return view('templates.admin.podcasts.edit', compact('podcast', 'js'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function store(PodcastStoreRequest $request): RedirectResponse
    {
        /** @var \App\Models\Audio\Podcast $podcast */
        $podcast = Podcast::create($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
            'duration' => 0,
        ])->toArray());
        $podcast->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));
        $songIdsWithPivot = collect($request->safe()->offsetGet('song_ids'))->mapWithKeys(fn (
            int $songId,
            int $index
        ) => [$songId => ['index' => $index]])->toArray();
        $podcast->songs()->sync($songIdsWithPivot);
        $authorsIdsWithPivot = collect($request->safe()->offsetGet('authors_ids'))->mapWithKeys(fn (
            int $authorId,
            int $index
        ) => [$authorId => ['index' => $index]])->toArray();
        $podcast->authors()->sync($authorsIdsWithPivot);
        $podcast->addMediaFromRequest('cover')->toMediaCollection('cover');
        if ($request->file('audio')) {
            Config::set('media-library.max_file_size', 1024 * 1024 * 600); // 600 Mo
            $audioFileID3 = GetId3::fromUploadedFile($request->file('audio'));
            $podcast->update([
                'duration' => (int) $audioFileID3->getPlaytimeSeconds() ?: $podcast->program()->get()[0]->duration,
            ]);
            $podcast->addMediaFromRequest('audio')->toMediaCollection('audio');
        } else {
            $podcast->update(['duration' => $podcast->program()->get()[0]->duration]);
        }

        $this->storeLocationFromRequest($request, $podcast);

        if ($podcast->active &&
            ($podcast->winmedia_audio_source_uploaded || $request->file('audio') || $podcast->hasMedia('audio'))
        ) {
            app(PodcastIndexService::class)->updateOrCreate($podcast);
        } else {
            app(PodcastIndexService::class)->delete($podcast);
        }
        app(CacheService::class)->clearBrowsePodcastsCache();

        return redirect()->route('podcasts.index')->with('toast_success', __('crud.orphan.created', [
            'entity' => 'Podcast',
            'name' => $podcast->title,
        ]));
    }

    /** @throws \Exception */
    public function edit(Podcast $podcast, Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Podcast',
            'detail' => $podcast->title,
        ]));
        share(['api' => ['song_url' => route('api.songs.search')]]);
        $js = mix('js/templates/admin/podcasts/edit.js');

        $duration = Date::now()->startOfDay()->seconds($podcast->duration);

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return view('templates.admin.podcasts.edit', compact('podcast', 'js', 'duration', 'previousParams'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function update(PodcastUpdateRequest $request, Podcast $podcast): RedirectResponse
    {
        Config::set('media-library.max_file_size', 1024 * 1024 * 600);
        $podcast->update($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
        ])->toArray());
        $podcast->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));
        $songIdsWithPivot = collect($request->safe()->offsetGet('song_ids'))->mapWithKeys(fn (
            int $songId,
            int $index
        ) => [$songId => ['index' => $index]])->toArray();
        $podcast->songs()->sync($songIdsWithPivot);
        $authorsIdsWithPivot = collect($request->safe()->offsetGet('authors_ids'))->mapWithKeys(fn (
            int $authorId,
            int $index
        ) => [$authorId => ['index' => $index]])->toArray();
        $podcast->authors()->sync($authorsIdsWithPivot);
        if ($request->file('cover')) {
            $podcast->addMediaFromRequest('cover')->toMediaCollection('cover');
        }
        if ($request->file('audio')) {
            Config::set('media-library.max_file_size', 1024 * 1024 * 600); // 600 Mo
            $audioFileID3 = GetId3::fromUploadedFile($request->file('audio'));
            $podcast->update([
                'duration' => (int) $audioFileID3->getPlaytimeSeconds() ?: $podcast->program()->get()[0]->duration,
            ]);
            $podcast->addMediaFromRequest('audio')->toMediaCollection('audio');
            $podcast->update(['winmedia_audio_source_uploaded' => false]);
        } elseif ($request->remove_audio_file) {
            $podcast->clearMediaCollection('audio');
            $podcast->update([
                'winmedia_audio_source_uploaded' => false,
                'duration' => $podcast->program()->get()[0]->duration,
            ]);
        }
        if (! $request->file('audio') && ! $podcast->winmedia_audio_source_uploaded && ! $podcast->hasMedia('audio') && ! $request->remove_audio_file) {
            $podcast->update([
                'duration' => Date::now()
                    ->startOfDay()
                    ->hour($request->duration_hours ?? 0)
                    ->minute($request->duration_minutes ?? 0)
                    ->second($request->duration_seconds ?? 0)
                    ->secondsSinceMidnight(),
            ]);
        }

        $this->updateLocationFromRequest($request, $podcast);

        if ($podcast->active &&
            ($request->file('audio') || (! $request->remove_audio_file && ($podcast->winmedia_audio_source_uploaded || $podcast->hasMedia('audio'))))
        ) {
            app(PodcastIndexService::class)->updateOrCreate($podcast);
        } else {
            app(PodcastIndexService::class)->delete($podcast);
        }
        app(CacheService::class)->clearBrowsePodcastsCache();

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to(route('podcast.edit', $podcast) . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Podcast',
                'name' => $podcast->title,
            ]));
    }

    /** @throws \Exception */
    public function destroy(Podcast $podcast): RedirectResponse
    {
        $podcast->delete();
        app(PodcastIndexService::class)->delete($podcast);
        app(CacheService::class)->clearBrowsePodcastsCache();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Podcast',
            'name' => $podcast->title,
        ]));
    }
}
