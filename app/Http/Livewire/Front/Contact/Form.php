<?php

namespace App\Http\Livewire\Front\Contact;

use App\Http\Requests\Contact\ContactPageSendMessageRequest;
use App\Models\Audio\Thematic;
use App\Models\Logs\LogContactFormMessage;
use App\Notifications\ContactFormMessage;
use App\Traits\WithFilepondUploadsCounter;
use Browser;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Notification;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\TemporaryUploadedFile;
use Livewire\WithFileUploads;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Form extends Component
{
    use WithFilepondUploadsCounter;
    use WithFileUploads;

    public const DESTINATIONS = [
        'equipe' => [
            'label_select' => 'Contacter l\'équipe',
            'label_mail' => 'Equipe',
            'mail' => '<EMAIL>',
            'steps' => ['Informations générales'],
            'description' => null,
        ],
        'reagir' => [
            'label_select' => 'Réagir',
            'label_mail' => 'Réagir',
            'mail' => '<EMAIL>',
            'steps' => ['Informations générales'],
            'description' => null,
        ],
        'evenement' => [
            'label_select' => 'Parler d\'un évènement',
            'label_mail' => 'Évènement',
            'mail' => '<EMAIL>',
            'steps' => ['Informations générales', 'Détail de l\'évènement'],
            'description' => null,
        ],
        'titre' => [
            'label_select' => 'Envoyer un ou des titre(s) à la programmation ou sur la plateforme',
            'label_mail' => 'Proposer un titre',
            'mail' => '<EMAIL>',
            'steps' => ['Informations générales', 'Détail musiques'],
            'description' => null,
        ],
        'probleme' => [
            'label_select' => 'Indiquer un problème technique',
            'label_mail' => 'Problème Technique',
            'mail' => '<EMAIL>',
            'steps' => ['Informations générales'],
            'description' => null,
        ],
        'rejoindre' => [
            'label_select' => 'Nous rejoindre',
            'label_mail' => null,
            'mail' => null,
            'steps' => [],
            'description' => null,
        ],
    ];

    public const TEAMS = [
        'redaction' => [
            'label_select' => 'Rédaction',
            'label_mail' => 'Equipe Rédaction',
            'mail' => '<EMAIL>',
            'description' => null,
        ],
        'programmation' => [
            'label_select' => 'Programmation',
            'label_mail' => 'Equipe Programmation',
            'mail' => '<EMAIL>',
            'description' => '<b>Pour nous proposer des titres à diffuser, merci d\'utiliser l\'option '
                . '"<a href="" wire:click.prevent="switchTo(\'titre\')">'
                . self::DESTINATIONS['titre']['label_select']
                . '</a></b>"',
        ],
        'administration' => [
            'label_select' => 'Administration',
            'label_mail' => 'Equipe Administration',
            'mail' => '<EMAIL>',
            'description' => null,
        ],
        'communication' => [
            'label_select' => 'Communication / Partenariat',
            'label_mail' => 'Equipe Communication / Partenariat',
            'mail' => '<EMAIL>',
            'description' => null,
        ],
        'technique' => [
            'label_select' => 'Technique',
            'label_mail' => 'Equipe Technique',
            'mail' => '<EMAIL>',
            'description' => null,
        ],
        'emi' => [
            'label_select' => 'Education aux médias',
            'label_mail' => 'Equipe EMI',
            'mail' => '<EMAIL>',
            'description' => null,
        ],
        'formation' => [
            'label_select' => 'Formation',
            'label_mail' => 'Equipe Formation',
            'mail' => '<EMAIL>',
            'description' => null,
        ],
    ];

    public const STATIONS = [
        'sun' => [
            'label_select' => 'SUN',
            'label_mail' => 'SUN',
            'mail' => '<EMAIL>',
        ],
        'junior' => [
            'label_select' => 'SUN Junior',
            'label_mail' => 'SUN Junior',
            'mail' => '<EMAIL>',
        ],
        'rock' => [
            'label_select' => 'SUN Rock',
            'label_mail' => 'SUN Rock',
            'mail' => null,
        ],
        'cineseries' => [
            'label_select' => 'SUN Ciné & Séries',
            'label_mail' => 'SUN Ciné & Séries',
            'mail' => null,
        ],
        /*'sport' => [
            'label_select' => 'SUN Sport',
            'label_mail' => 'SUN Sport',
            'mail' => '<EMAIL>',
        ],*/
        'electro' => [
            'label_select' => 'SUN Electro',
            'label_mail' => 'SUN Electro',
            'mail' => '<EMAIL>',
        ],
        'metal' => [
            'label_select' => 'SUN Metal',
            'label_mail' => 'SUN Metal',
            'mail' => '<EMAIL>',
        ],
        'nouvo' => [
            'label_select' => 'SUN Nouvo',
            'label_mail' => 'SUN Nouvo',
            'mail' => '<EMAIL>',
        ],
        'soulfunk' => [
            'label_select' => 'SUN Soul & Funk',
            'label_mail' => 'SUN Soul & Funk',
            'mail' => null,
        ],
        'hiphop' => [
            'label_select' => 'SUN Hip-Hop',
            'label_mail' => 'SUN Hip-Hop',
            'mail' => '<EMAIL>',
        ],
        'jazz' => [
            'label_select' => 'SUN Jazz',
            'label_mail' => 'SUN Jazz',
            'mail' => '<EMAIL>',
        ],
        'classique' => [
            'label_select' => 'SUN Classique',
            'label_mail' => 'SUN Classique',
            'mail' => '<EMAIL>',
        ],
    ];

    public const GENRES_OPTIONS = ['Acid Jazz', 'Acoustique', 'Alternative et punk', 'Autres', 'B.O.F.', 'Blues', 'Blues Rock', 'Chanson', 'Classique', 'Country & Folk',
        'Dance', 'Disco', 'Electro', 'Electronica', 'Folk', 'Funk', 'Générique', 'House', 'Instrumental', 'Jazz', 'Lounge', 'Metal', 'Musique traditionnelle',
        'New Disco', 'New Wave', 'Pop', 'Punk', 'Rap & Hip-Hop', 'Reggae', 'Rock', 'Rock & Roll', 'Slow', 'Soul', 'Urban', 'World Music'];

    public const REGIONS_OPTIONS = ['Auvergne-Rhône-Alpes', 'Bourgogne-Franche-Comté', 'Bretagne', 'Centre-Val de Loire', 'Corse', 'Grand Est', 'Guadeloupe', 'Guyane',
        'Hauts-de-France', 'Île-de-France', 'La Réunion', 'Martinique', 'Mayotte', 'Normandie', 'Nouvelle-Aquitaine', 'Occitanie', 'Pays de la Loire',
        'Provence-Alpes-Côte d\'Azur', 'Autre (hors France)'];

    public const DEPARTEMENTS_OPTIONS = ['Loire-Atlantique', 'Maine-et-Loire', 'Vendée'];

    public const PROBLEM_TYPES_OPTIONS = ['L\'écoute et réception en FM / DAB+', 'L\'application mySUN (site web, application mobile)',
        'L\'écoute en streaming (services externes, enceintes connectées, ...)',  'Autre'];

    public const PROBLEM_DEVICES_OPTIONS = ['Smartphone Android', 'Smartphone iOS', 'Tablette Android', 'Tablette iPad',
        'Ordinateur Windows', 'Ordinateur macOS', 'Ordinateur Linux', 'Autre'];

    public const PERSON_TYPES_OPTIONS = ['Artiste', 'Label', 'Attaché de presse', 'Auditeur'];

    public const AUDIENCE_AGES_OPTIONS = ['Tout public', 'Adulte', 'Adolescent', 'Enfant', 'Tout-petit', 'Professionnel'];

    public const EVENT_TYPES_OPTIONS = ['Concert', 'Festival', 'Cinéma', 'Spectacle', 'Expo', 'Conférence', 'Atelier', 'Marché', 'Théâtre', 'Visite et balade', 'Autre'];

    public string $uniqId;

    public array $destinationsOptions = [];

    public array $teamsOptions = [];

    public array $stationsOptions = [];

    public array $thematicsOptions = [];

    public int $maxStep;

    public int $currentStep;

    // Global

    public string $user_agent = '';

    public string $desired_destination = '';

    public string $desired_team = '';

    public string $desired_station = '';

    public string $first_name = '';

    public string $last_name = '';

    public string $email = '';

    public string $phone_number = '';

    public string $message = '';

    // Champs pièces jointes

    /** @var null|string[] */
    public $attachments_general = [];

    /** @var null|string[] */
    public $attachments_audio = [];

    /** @var null|string[] */
    public $attachments_image = [];

    /** @var null|string[] */
    public $attachments_general_end = [];

    public array $filepond_metadata = [];

    public array $audio_metadata = [];

    public array $attachments_audio_cover = [];

    public array $attachments_audio_performer_picture = [];

    // Champs spécifiques

    // Redaction
    public string $redaction_departement = '';

    // Programmation

    public string $programmation_live_session = 'Non';

    // Problème

    public string $problem_type = '';

    public string $problem_device = '';

    public string $problem_device_name = '';

    public bool $problem_allow_user_agent = false;

    public string $problem_localisation = '';

    // Evenement
    public string $event_structure_name = '';

    public string $event_already_solicited = 'Non';

    public string $event_stations = '';

    public string $event_date = '';

    public string $event_time_start = '';

    public string $event_time_end = '';

    public string $event_thematic = '';

    public string $event_type = '';

    public string $event_name = '';

    public string $event_description = '';

    public string $event_free = 'Non';

    public string $event_address = '';

    public string $event_audience_age = '';

    public int $event_giveaway_tickets = 0;

    public string $event_link = '';

    public string $event_buy_link = '';

    // Soumettre un titre

    public string $track_person_type = '';

    public string $track_structure_name = '';

    protected function setUniqId()
    {
        $this->uniqId = uniqid('contact_form_');
    }

    protected function rules()
    {
        $request = new ContactPageSendMessageRequest();

        return $request->rules($this->currentStep, $this->desired_destination, $this->desired_team);
    }

    protected function messages()
    {
        $request = new ContactPageSendMessageRequest();

        return $request->messages();
    }

    public function mount()
    {
        $this->user_agent = Browser::userAgent();
        $this->currentStep = 1;
        $this->maxStep = $this->desired_destination ? count(self::DESTINATIONS[$this->desired_destination]['steps']) : 1;
        $this->setOptions();
        $this->setUniqId();
    }

    public function render(): View
    {
        return view('livewire.front.contact.form');
    }

    public function switchTo(string $destination, string $team = '')
    {
        $this->desired_destination = $destination;
        $this->desired_team = $team;
    }

    protected function resetForm()
    {
        $this->emit('contactformpicker:reset');
        $this->emit('contactformtags:reset');

        $this->reset([
            'desired_destination',
            'desired_team',
            'desired_station',
            'first_name',
            'last_name',
            'email',
            'phone_number',
            'message',
            'attachments_general',
            'attachments_audio',
            'attachments_image',
            'attachments_general_end',
            'filepond_metadata',
            'audio_metadata',
            'attachments_audio_cover',
            'attachments_audio_performer_picture',
            'redaction_departement',
            'programmation_live_session',
            'problem_type',
            'problem_device',
            'problem_device_name',
            'problem_allow_user_agent',
            'problem_localisation',
            'event_structure_name',
            'event_already_solicited',
            'event_stations',
            'event_date',
            'event_time_start',
            'event_time_end',
            'event_thematic',
            'event_type',
            'event_name',
            'event_description',
            'event_free',
            'event_address',
            'event_audience_age',
            'event_giveaway_tickets',
            'event_link',
            'event_buy_link',
            'track_person_type',
            'track_structure_name',
        ]);

        $this->user_agent = Browser::userAgent();

        $this->currentStep = 1;
        $this->maxStep = $this->desired_destination ? count(self::DESTINATIONS[$this->desired_destination]['steps']) : 1;

        $this->setUniqId();

        $this->emit('scroll:top', 'window', 0);
    }

    public function submit()
    {
        $validated = $this->validate();

        $mailDestination = $this->getMailDestination($this->desired_destination, $this->desired_team, $this->desired_station);

        //dd($validated, $mailDestination);

        $validatedExceptUploads = Arr::except(
            $validated,
            [
                'attachments_general',
                'attachments_audio',
                'attachments_image',
                'attachments_audio_cover',
                'attachments_audio_performer_picture',
                'attachments_general_end',
            ]
        );

        $userAgent = ($validated['desired_destination'] === 'probleme' && $validated['problem_allow_user_agent']) ? $this->user_agent : null;

        $validatedForStore = $validatedExceptUploads;

        $validatedForStore['desired_destination'] = $mailDestination['mail'];

        if ($userAgent) {
            $validatedForStore['user_agent'] = $userAgent;
        }

        $attachmentsGeneralTotalSize = 0;
        $attachmentsImageTotalSize = 0;

        foreach (data_get($validated, 'attachments_general') as $attachmentGeneral) {
            $attachmentsGeneralTotalSize += $attachmentGeneral->getSize();
        }
        foreach (data_get($validated, 'attachments_general_end') as $attachmentGeneralEnd) {
            $attachmentsGeneralTotalSize += $attachmentGeneralEnd->getSize();
        }
        foreach (data_get($validated, 'attachments_image') as $attachmentImage) {
            $attachmentsImageTotalSize += $attachmentImage->getSize();
        }

        $log = $this->storeData($validated, $validatedForStore);

        $this->sendNotification(
            $validatedExceptUploads,
            $mailDestination,
            $userAgent,
            $log->getMedia('attachments_general'),
            $log->getMedia('attachments_audio'),
            $log->getMedia('attachments_image'),
            $log->getMedia('attachments_audio_cover'),
            $log->getMedia('attachments_audio_performer_picture'),
            $attachmentsGeneralTotalSize,
            $attachmentsImageTotalSize,
            false
        );
        $this->sendNotification(
            $validatedExceptUploads,
            $mailDestination,
            $userAgent,
            $log->getMedia('attachments_general'),
            $log->getMedia('attachments_audio'),
            $log->getMedia('attachments_image'),
            $log->getMedia('attachments_audio_cover'),
            $log->getMedia('attachments_audio_performer_picture'),
            $attachmentsGeneralTotalSize,
            $attachmentsImageTotalSize,
            true
        );

        $this->dispatchBrowserEvent('toast:success', ['title' => __('Your message has been sent, we have emailed you a copy.')]);

        $this->resetForm();
    }

    protected function storeData(array $validated, array $validatedForStore): LogContactFormMessage
    {
        $log = LogContactFormMessage::create(['data' => $validatedForStore]);

        $metadataAttachmentsGeneral = array_reduce($this->filepond_metadata, function ($carry, $item) {
            return $item['model'] === 'attachments_general' ? $item : null;
        });

        foreach (data_get($validated, 'attachments_general') as $attachmentGeneral) {
            $metadata = null;
            if ($metadataAttachmentsGeneral) {
                $filenameId = $attachmentGeneral->getFilename();
                $metadata = array_reduce($metadataAttachmentsGeneral['data'], function ($carry, $item) use ($filenameId) {
                    return $item['fileServerId'] === $filenameId ? $item : $carry;
                });
            }

            if (isset($metadata['crop']) && isset($metadata['size'])) {
                $log->addMedia($attachmentGeneral)
                    ->withCustomProperties([
                        'crop' => $metadata['crop'],
                        'size' => $metadata['size'],
                    ])
                    ->toMediaCollection('attachments_general');
            } else {
                $log->addMedia($attachmentGeneral)
                    ->toMediaCollection('attachments_general');
            }
        }

        $metadataAttachmentsGeneralEnd = array_reduce($this->filepond_metadata, function ($carry, $item) {
            return $item['model'] === 'attachments_general_end' ? $item : null;
        });

        foreach (data_get($validated, 'attachments_general_end') as $attachmentGeneralEnd) {
            $metadata = null;
            if ($metadataAttachmentsGeneralEnd) {
                $filenameId = $attachmentGeneralEnd->getFilename();
                $metadata = array_reduce($metadataAttachmentsGeneralEnd['data'], function ($carry, $item) use ($filenameId) {
                    return $item['fileServerId'] === $filenameId ? $item : $carry;
                });
            }

            if (isset($metadata['crop']) && isset($metadata['size'])) {
                $log->addMedia($attachmentGeneralEnd)
                    ->withCustomProperties([
                        'crop' => $metadata['crop'],
                        'size' => $metadata['size'],
                    ])
                    ->toMediaCollection('attachments_general');
            } else {
                $log->addMedia($attachmentGeneralEnd)
                    ->toMediaCollection('attachments_general');
            }
        }

        $metadataAttachmentsImage = array_reduce($this->filepond_metadata, function ($carry, $item) {
            return $item['model'] === 'attachments_image' ? $item : null;
        });

        foreach (data_get($validated, 'attachments_image') as $attachmentImage) {
            $metadata = null;
            if ($metadataAttachmentsImage) {
                $filenameId = $attachmentImage->getFilename();
                $metadata = array_reduce($metadataAttachmentsImage['data'], function ($carry, $item) use ($filenameId) {
                    return $item['fileServerId'] === $filenameId ? $item : $carry;
                });
            }

            if (isset($metadata['crop']) && isset($metadata['size'])) {
                $log->addMedia($attachmentImage)
                    ->withCustomProperties([
                        'crop' => $metadata['crop'],
                        'size' => $metadata['size'],
                    ])
                    ->toMediaCollection('attachments_image');
            } else {
                $log->addMedia($attachmentImage)
                    ->toMediaCollection('attachments_image');
            }
        }

        foreach (data_get($validated, 'attachments_audio') as $attachmentAudio) {
            $filenameId = pathinfo($attachmentAudio->getFilename(), PATHINFO_FILENAME);

            $log->addMedia($attachmentAudio)
                ->withCustomProperties([
                    'audio_file_metadata_id' => $filenameId,
                ])
                ->toMediaCollection('attachments_audio');

            $attachmentsAudioCover = data_get($validated, 'attachments_audio_cover');
            if (is_array($attachmentsAudioCover)) {
                if (array_key_exists($filenameId, $attachmentsAudioCover)) {
                    $attachmentAudioCover = $attachmentsAudioCover[$filenameId];
                    $log->addMedia($attachmentAudioCover)
                        ->withCustomProperties([
                            'audio_file_metadata_id' => $filenameId,
                        ])
                        ->toMediaCollection('attachments_audio_cover');
                }
            }

            $attachmentsAudioPerformerPicture = data_get($validated, 'attachments_audio_performer_picture');
            if (is_array($attachmentsAudioPerformerPicture)) {
                if (array_key_exists($filenameId, $attachmentsAudioPerformerPicture)) {
                    $attachmentAudioPerformerPicture = $attachmentsAudioPerformerPicture[$filenameId];
                    $log->addMedia($attachmentAudioPerformerPicture)
                        ->withCustomProperties([
                            'audio_file_metadata_id' => $filenameId,
                        ])
                        ->toMediaCollection('attachments_audio_performer_picture');
                }
            }
        }

        return $log;
    }

    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    protected function sendNotification(
        array $validated,
        array $mailDestination,
        ?string $userAgent,
        MediaCollection $attachmentsGeneral,
        MediaCollection $attachmentsAudio,
        MediaCollection $attachmentsImage,
        MediaCollection $attachmentsAudioCover,
        MediaCollection $attachmentsAudioPerformerPicture,
        int $attachmentsGeneralTotalSize,
        int $attachmentsImageTotalSize,
        bool $isCopyToSender
    ): void {
        $notification = (new ContactFormMessage(
            $validated,
            $validated['desired_destination'],
            $mailDestination['label'],
            $mailDestination['mail'],
            $userAgent,
            $attachmentsGeneral,
            $attachmentsAudio,
            $attachmentsImage,
            $attachmentsAudioCover,
            $attachmentsAudioPerformerPicture,
            $attachmentsGeneralTotalSize,
            $attachmentsImageTotalSize,
            $isCopyToSender
        ))->locale(app()->getLocale());
        Notification::route(
            'mail',
            $isCopyToSender ? $validated['email'] : $mailDestination['mail']
        )->notify($notification);
    }

    public function changeDesiredDestination()
    {
        if ($this->desired_destination === 'rejoindre') {
            return redirect()->route('joinUs.page.show');
        }
        $this->currentStep = 1;
        $this->maxStep = $this->desired_destination ? count(self::DESTINATIONS[$this->desired_destination]['steps']) : 1;
    }

    protected function setOptions()
    {
        $this->destinationsOptions = array_map(function ($value) {
            return $value['label_select'];
        }, self::DESTINATIONS);
        $this->teamsOptions = array_map(function ($value) {
            return $value['label_select'];
        }, self::TEAMS);
        $this->stationsOptions = array_map(function ($value) {
            return $value['label_select'];
        }, self::STATIONS);
        $this->thematicsOptions = Thematic::all()->pluck('title', 'id')->toArray();
    }

    protected function getMailDestination(
        string $desiredDestination,
        ?string $desiredTeam = null,
        ?string $desiredStation = null)
    {
        if ($desiredDestination === 'equipe' && $desiredTeam !== null) {
            if ($desiredTeam === 'programmation' && $desiredStation !== null) {
                // Equipe programmation (label: desiredTeam + label desiredStation / mail: desiredStation si présent, sinon mail desiredTeam)
                return [
                    'label' => self::TEAMS[$desiredTeam]['label_mail'] . ' ' . self::STATIONS[$desiredStation]['label_mail'],
                    'mail' => self::STATIONS[$desiredStation]['mail'] ?? self::TEAMS[$desiredTeam]['mail'],
                ];
            } else {
                return [
                    'label' => self::TEAMS[$desiredTeam]['label_mail'],
                    'mail' => self::TEAMS[$desiredTeam]['mail'],
                ];
            }
        } elseif ($desiredDestination === 'titre' && $desiredStation !== null) {
            // Soumettre titre (label: desiredDestination + label desiredStation / mail: desiredStation si présent, sinon mail desiredDestination)
            return [
                'label' => self::DESTINATIONS[$desiredDestination]['label_mail'] . ' ' . self::STATIONS[$desiredStation]['label_mail'],
                'mail' => self::STATIONS[$desiredStation]['mail'] ?? self::DESTINATIONS[$desiredDestination]['mail'],
            ];
        }

        return [
            'label' => self::DESTINATIONS[$desiredDestination]['label_mail'],
            'mail' => self::DESTINATIONS[$desiredDestination]['mail'],
        ];
    }

    protected function prepareAudioMetadata()
    {
        /** @var TemporaryUploadedFile $attachmentAudio */
        foreach ($this->attachments_audio as $attachmentAudio) {
            $filenameId = pathinfo($attachmentAudio->getFilename(), PATHINFO_FILENAME);

            if (! array_key_exists($filenameId, $this->audio_metadata)) {
                $this->audio_metadata[$filenameId] = [
                    'performer' => '',
                    'title' => '',
                    'album' => '',
                    'genre' => '',
                    'release_date' => '',
                    'embargo' => '',
                    'localisation_region' => '',
                    'localisation_town' => '',
                    'buy_link' => '',
                    'programmation_live_session' => 'Non',
                ];
            }
        }
    }

    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
            $this->emit('scroll:top', 'window', 0, 200);
        }
    }

    public function nextStep()
    {
        if ($this->currentStep < $this->maxStep) {
            $this->validate();
            $this->currentStep++;
            $this->emit('scroll:top', 'window', 0, 200);
            if ($this->desired_destination === 'titre') {
                $this->prepareAudioMetadata();
                $this->emit('datepickerwinmedia:init');
                $this->emit('datepickerwrap:init');
            }
        }
    }
}
