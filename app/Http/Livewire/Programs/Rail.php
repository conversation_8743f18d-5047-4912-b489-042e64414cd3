<?php

namespace App\Http\Livewire\Programs;

use App\Models\Radio\Program;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Rail extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = true;

    public ?string $title = null;

    public string $railName;

    public Collection $programs;

    public ?string $showAllRoute = null;

    public array $showAllParams = [];

    public array $selectedProgramIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllPrograms = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function init(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->updateAudioProgramsStatuses();
        $this->initialized = true;
    }

    public function render(): View
    {
        return view('livewire.programs.rail');
    }

    protected function updateAudioProgramsStatuses(): void
    {
        if ($this->pauseAllPrograms) {
            $this->programs = $this->programs->map(function (Program $program) {
                $program->selected = in_array($program->id, $this->selectedProgramIds, true);
                $program->playing = false;

                return $program;
            });

            return;
        }
        $this->selectedProgramIds = [];
        $this->programs = $this->programs->map(function (Program $program) {
            $isPlaying = $program::class === $this->playedAudioSourceClass
                && $program->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedProgramIds[] = $program->id;
            }
            $program->selected = $isPlaying;
            $program->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $program;
        });
        $this->emit('rail:load', ['railName' => $this->railName]);
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllPrograms = false;
        $this->updateAudioProgramsStatuses();
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllPrograms = true;
        $this->updateAudioProgramsStatuses();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllPrograms = false;
        $this->updateAudioProgramsStatuses();
    }
}
