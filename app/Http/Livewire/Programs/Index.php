<?php

namespace App\Http\Livewire\Programs;

use App\Models\Audio\Podcast;
use App\Models\Radio\Program;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Index extends Component
{
    public bool $initialized = false;

    public ?int $selectedRadioStationId = null;

    public ?string $selectedDay = null;

    public Collection $currentPrograms;

    public Collection $podcasts;

    public array $allWeeks = [];

    public array $visibleDays = [];

    public int $currentWeekOffset = 0;

    public int $totalWeeksRange = 5; // 5 semaines de données (2 passées + actuelle + 2 futures)

    public array $selectedPodcastIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllPodcasts = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'radio:station:universe:updated' => 'setSelectedRadioStationId',
    ];

    public function mount(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'programs', livewireComponent: $this);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStationId($selectedRadioStationId);
        $this->setWeeksData();
        $this->setCurrentWeekOffset();
        $this->setVisibleDays();
        $this->selectedDay = Date::today()->toDateString();
        $this->initialized = true;
    }

    public function setSelectedRadioStationId(int $selectedRadioStationId): void
    {
        $this->selectedRadioStationId = $selectedRadioStationId;
    }

    protected function setWeeksData(): void
    {
        // Générer les semaines (du lundi au dimanche)
        $today = Date::today();
        $currentWeekStart = $today->copy()->startOfWeek(); // Lundi de la semaine actuelle

        $this->allWeeks = [];

        // Générer les semaines (2 passées + actuelle + 2 futures)
        for ($weekOffset = -2; $weekOffset <= 2; $weekOffset++) {
            $weekStart = $currentWeekStart->copy()->addWeeks($weekOffset);
            $week = [];

            // Générer les 7 jours de la semaine (lundi à dimanche)
            for ($dayOffset = 0; $dayOffset < 7; $dayOffset++) {
                $week[] = $weekStart->copy()->addDays($dayOffset)->toDateString();
            }

            $this->allWeeks[] = $week;
        }
    }

    protected function setCurrentWeekOffset(): void
    {
        // Trouver la semaine actuelle (offset 2 car on a 2 semaines passées)
        $this->currentWeekOffset = 2;
    }

    protected function setVisibleDays(): void
    {
        if (isset($this->allWeeks[$this->currentWeekOffset])) {
            $this->visibleDays = $this->allWeeks[$this->currentWeekOffset];
        } else {
            $this->visibleDays = [];
        }
    }

    public function updateSelectedDay(string $selectedDay): void
    {
        $this->selectedDay = $selectedDay;
        $this->setPodcasts();
    }

    public function navigateToPreviousPeriod(): void
    {
        if ($this->canNavigateToPrevious()) {
            $this->currentWeekOffset--;
            $this->setVisibleDays();

            // Ne pas changer le jour sélectionné, juste mettre à jour les podcasts si le jour est toujours visible
            if (in_array($this->selectedDay, $this->visibleDays)) {
                $this->setPodcasts();
            }
        }
    }

    public function navigateToNextPeriod(): void
    {
        if ($this->canNavigateToNext()) {
            $this->currentWeekOffset++;
            $this->setVisibleDays();

            // Ne pas changer le jour sélectionné, juste mettre à jour les podcasts si le jour est toujours visible
            if (in_array($this->selectedDay, $this->visibleDays)) {
                $this->setPodcasts();
            }
        }
    }

    public function canNavigateToPrevious(): bool
    {
        return $this->currentWeekOffset > 0;
    }

    public function canNavigateToNext(): bool
    {
        return $this->currentWeekOffset < (count($this->allWeeks) - 1);
    }

    public function getWeekTitle(): string
    {
        if (!$this->initialized || empty($this->visibleDays)) {
            return 'Cette semaine';
        }

        // Semaine courante (offset 2)
        if ($this->currentWeekOffset === 2) {
            return 'Cette semaine';
        }

        // Semaine précédente (offset 1)
        if ($this->currentWeekOffset === 1) {
            return 'Semaine précédente';
        }

        // Semaine suivante (offset 3)
        if ($this->currentWeekOffset === 3) {
            return 'Semaine suivante';
        }

        // Autres semaines : afficher la plage de dates
        $startDate = Date::parse($this->visibleDays[0]);
        $endDate = Date::parse($this->visibleDays[6]);

        // Si c'est le même mois
        if ($startDate->month === $endDate->month) {
            return sprintf(
                'Semaine du %d au %d %s',
                $startDate->day,
                $endDate->day,
                $startDate->isoFormat('MMMM Y')
            );
        }

        // Si c'est des mois différents
        return sprintf(
            'Semaine du %s au %s',
            $startDate->isoFormat('D MMM'),
            $endDate->isoFormat('D MMM Y')
        );
    }

    /** @throws \Exception */
    public function setCurrentPrograms(): void
    {
        // Choisir la version optimisée (SQL brute) ou Eloquent selon les préférences
        $this->currentPrograms = $this->getCurrentProgramsWithEloquent();
        // Alternative ultra-optimisée : $this->currentPrograms = $this->getCurrentProgramsOptimized();
    }

    /**
     * Version Eloquent optimisée - plus lisible et maintenable
     */
    private function getCurrentProgramsWithEloquent(): Collection
    {
        // Conditions communes pour les podcasts avec toutes les optimisations
        $podcastConditions = function (Builder $query) {
            /** @phpstan-ignore-next-line */
            $query->hasAudio()
                ->active()
                ->where('published_at', '<=', Date::now())
                ->where(function (Builder $subWhereQuery) {
                    // Condition optimisée sur la station de radio
                    $subWhereQuery->whereHas('radioStations', function (Builder $radioQuery) {
                        $radioQuery->where('radio_stations.id', $this->selectedRadioStationId);
                    })->orWhereDoesntHave('radioStations');
                });
        };

        return Program::with(['media', 'podcasts'])
            //->withCount(['podcasts' => $podcastConditions])
            ->withMax(['podcasts' => $podcastConditions], 'published_at')
            ->whereHas('podcasts', $podcastConditions)
            ->orderByRaw('podcasts_max_published_at DESC NULLS LAST')
            ->orderBy('title')
            ->limit(20)
            ->get();
    }

    public function setPodcasts(): void
    {
        $this->podcasts = Podcast::with(['media', 'program.subPrograms'])
            ->where('type', Podcast::TYPE_REPLAY)
            ->whereDate('published_at', Date::parse($this->selectedDay))
            ->where('active', true)
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereRelation(
                    'radioStations',
                    fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStationId)
                )->orWhereDoesntHave('radioStations');
            })
            ->orderBy('published_at')
            ->get();
        $this->updatePodcastStatuses();
    }

    public function updatePodcastStatuses(): void
    {
        if ($this->pauseAllPodcasts) {
            $this->podcasts->map(function (Podcast $podcast) {
                $podcast->programSelected = in_array($podcast->id, $this->selectedPodcastIds, true);
                $podcast->playing = false;
                $podcast->subPodcasts = $this->podcasts->whereIn(
                    'program_id',
                    $podcast->program->subPrograms->pluck('id')
                );

                return $podcast;
            });

            return;
        }
        $this->selectedPodcastIds = [];
        $this->podcasts = $this->podcasts->map(function (Podcast $podcast) {
            $isPlaying = $podcast::class === $this->playedAudioSourceClass
                && $podcast->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedPodcastIds[] = $podcast->id;
            }
            $podcast->programSelected = $isPlaying;
            $podcast->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
            $podcast->subPodcasts = $this->podcasts->whereIn('program_id', $podcast->program->subPrograms->pluck('id'));

            return $podcast;
        });
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllPodcasts = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllPodcasts = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllPodcasts = false;
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->setPodcasts();
            $this->setCurrentPrograms();
        }

        return view('livewire.programs.index');
    }
}
