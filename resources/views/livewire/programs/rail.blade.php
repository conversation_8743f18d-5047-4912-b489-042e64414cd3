<div wire:init="init">
    <x:front.rail :wire:key="'program-rail-' . $railName"
                  class="m-n1-5"
                  :title="$title"
                  :name="$railName"
                  :showAllRoute="$showAllRoute"
                  :showAllParams="$showAllParams">
        @if($initialized && $contentLoaded)
            @forelse($programs as $program)
                <x:front.audio.program-card :wire:key="uniqid('rail-program-card-' . $program->id, true)"
                                            class="m-1-5"
                                            :programId="$program->id"
                                            :programName="$program->title"
                                            :programCoverThumbUrl="$program->getFirstMediaUrl('cover', 'medium')"
                                            :podcastsCount="$program->podcasts->count()"
                                            :selected="$podcast->selected ?? false"
                                            :playing="$podcast->playing ?? false"/>
            @empty
                <div class="m-1-5">
                    Aucun program n'a été trouvé.
                </div>
            @endforelse
        @else
            <div class="d-flex align-items-center">
                <div class="spinner-border me-3" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                Chargement en cours...
            </div>
        @endif
    </x:front.rail>
</div>
