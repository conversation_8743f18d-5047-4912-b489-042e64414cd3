// Components
@import '../../components/front/audio/program-line';
@import '../../components/front/audio/program-card';

.programs-navigation-container {
    min-height: pxToRem(81); // Hauteur minimale pour éviter les décalages

    button.navigation-btn {
        width: pxToRem(65) !important;
        height: pxToRem(81) !important;
        border: none !important;
        border-radius: pxToRem(12) !important;
        background-color: $gray-300 !important;
        color: $gray-600 !important; // Couleur plus foncée pour les flèches
        transition: all 0.3s ease;
        box-shadow: none !important;
        outline: none !important;
        align-self: center;

        // Styles spécifiques pour les icônes SVG
        .icon {
            fill: $gray-600 !important;
        }

        // Espacement cohérent avec les jours (mx-lg-2 = 0.5rem = 8px)
        &.me-2 {
            margin-right: 0.5rem !important; // Même espacement que mx-lg-2 des jours
        }

        &.ms-2 {
            margin-left: 0.5rem !important; // Même espacement que mx-lg-2 des jours
        }

        &:focus {
            box-shadow: none !important;
            outline: none !important;
            border: none !important;
            background-color: $gray-300 !important;
        }

        &:active {
            box-shadow: none !important;
            outline: none !important;
            border: none !important;
            background-color: $gray-300 !important;
        }

        &:not(:disabled):hover {
            background-color: $secondary !important;
            color: white !important;
            border: none !important;

            .icon {
                fill: white !important;
            }
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        // Force suppression des styles Bootstrap
        &.btn {
            --bs-btn-border-width: 0 !important;
            --bs-btn-border-color: transparent !important;
            --bs-btn-focus-shadow-rgb: none !important;
            --bs-btn-active-border-color: transparent !important;
            --bs-btn-hover-border-color: transparent !important;
        }
    }
}

#programs-day-selector {
    overflow-x: auto;
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE/Edge
    scroll-behavior: smooth;

    &::-webkit-scrollbar {
        display: none; // Chrome/Safari
    }

    .day {
        width: pxToRem(65);
        height: pxToRem(81);
        flex-shrink: 0; // Empêche la compression des jours
        background: none;

        &:not(.active) {
            .content {
                background-color: $gray-300;
            }
        }

        &.active, &:hover {
            .content {
                background-color: $secondary !important;
                color: white;
                height: pxToRem(58) !important;
                transition-duration: 0.5s;
            }

            &:after {
                display: inline-block;
                content: '';
                border-top: pxToRem(5) solid $primary;
                position: absolute;
                width: 50%;
                left: 50%;
                transform: translateX(-50%);
                bottom: 0;
            }
        }
    }
}



#programs-day-selector-mobile {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    scroll-behavior: smooth;

    &::-webkit-scrollbar {
        display: none;
    }

    .day {
        width: pxToRem(65);
        height: pxToRem(81);
        flex-shrink: 0;
        background: none;

        &:not(.active) {
            .content {
                background-color: $gray-300;
            }
        }

        &.active, &:hover {
            .content {
                background-color: $secondary !important;
                color: white;
                height: pxToRem(58) !important;
                transition-duration: 0.5s;
            }

            &:after {
                display: inline-block;
                content: '';
                border-top: pxToRem(5) solid $primary;
                position: absolute;
                width: 50%;
                left: 50%;
                transform: translateX(-50%);
                bottom: 0;
            }
        }
    }
}

// Responsive adjustments
@media (max-width: 768px) {
    #programs-day-selector-mobile {
        .day {
            width: pxToRem(55);
            height: pxToRem(71);
            margin: 0 pxToRem(4) !important;

            .content {
                font-size: 0.9rem;

                p {
                    font-size: 0.9rem !important;

                    &.small {
                        font-size: 0.75rem !important;
                    }
                }
            }
        }
    }
}

// Très petits écrans
@media (max-width: 480px) {
    #programs-day-selector-mobile {
        .day {
            width: pxToRem(50);
            height: pxToRem(65);
            margin: 0 pxToRem(2) !important;

            .content {
                font-size: 0.8rem;

                p {
                    font-size: 0.8rem !important;

                    &.small {
                        font-size: 0.7rem !important;
                    }
                }
            }
        }
    }
}

