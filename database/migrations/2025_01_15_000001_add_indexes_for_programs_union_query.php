<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('program_recurrences', function (Blueprint $table) {
            // Index pour optimiser la jointure avec programs
            // (program_id est déjà indexé via la foreign key, mais on s'assure)
            if (!$this->indexExists('program_recurrences', 'program_recurrences_program_id_index')) {
                $table->index('program_id', 'idx_program_recurrences_program_id');
            }
        });

        Schema::table('podcasts', function (Blueprint $table) {
            // Index composite pour les podcasts originaux récents
            $table->index(['type', 'published_at'], 'idx_podcasts_type_published');
            
            // Index composite incluant program_id pour optimiser la jointure
            $table->index(['program_id', 'type', 'published_at'], 'idx_podcasts_program_type_published');
        });
    }

    public function down(): void
    {
        Schema::table('podcasts', function (Blueprint $table) {
            $table->dropIndex('idx_podcasts_program_type_published');
            $table->dropIndex('idx_podcasts_type_published');
        });

        Schema::table('program_recurrences', function (Blueprint $table) {
            if ($this->indexExists('program_recurrences', 'idx_program_recurrences_program_id')) {
                $table->dropIndex('idx_program_recurrences_program_id');
            }
        });
    }

    /**
     * Vérifier si un index existe
     */
    private function indexExists(string $table, string $indexName): bool
    {
        $connection = Schema::getConnection();
        $schemaManager = $connection->getDoctrineSchemaManager();
        $indexes = $schemaManager->listTableIndexes($table);
        
        return isset($indexes[$indexName]);
    }
};
