<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('podcasts', function (Blueprint $table) {
            // Index composite pour les conditions principales des podcasts
            $table->index(['active', 'published_at'], 'idx_podcasts_active_published');
            
            // Index pour la recherche par date
            $table->index(['published_at', 'active'], 'idx_podcasts_published_active');
            
            // Index pour le type de podcast
            $table->index(['type', 'active'], 'idx_podcasts_type_active');
        });

        Schema::table('media', function (Blueprint $table) {
            // Index composite pour la recherche des médias audio des podcasts
            $table->index(['model_type', 'model_id', 'collection_name'], 'idx_media_model_collection');
        });

        Schema::table('podcasts_radio_stations', function (Blueprint $table) {
            // Index pour optimiser les jointures avec les stations de radio
            $table->index('radio_station_id', 'idx_podcasts_radio_stations_radio_id');
        });
    }

    public function down(): void
    {
        Schema::table('podcasts_radio_stations', function (Blueprint $table) {
            $table->dropIndex('idx_podcasts_radio_stations_radio_id');
        });

        Schema::table('media', function (Blueprint $table) {
            $table->dropIndex('idx_media_model_collection');
        });

        Schema::table('podcasts', function (Blueprint $table) {
            $table->dropIndex('idx_podcasts_type_active');
            $table->dropIndex('idx_podcasts_published_active');
            $table->dropIndex('idx_podcasts_active_published');
        });
    }
};
